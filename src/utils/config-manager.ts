import { readFile } from "fs/promises";
import { join, resolve } from "path";
import { existsSync } from "fs";

export interface ServerConfig {
  libraryPath: string;
  cacheDir: string;
  watchFiles: boolean;
  analysisRules: AnalysisRules;
  completionSettings: CompletionSettings;
}

export interface AnalysisRules {
  enforceOnPush: boolean;
  checkAccessibility: boolean;
  validateImports: boolean;
  performanceChecks: boolean;
}

export interface CompletionSettings {
  includePrivateMembers: boolean;
  fuzzyMatching: boolean;
  maxSuggestions: number;
  prioritizeRecentlyUsed: boolean;
}

export class ConfigManager {
  private config: ServerConfig;
  private defaultConfig: ServerConfig = {
    libraryPath: process.cwd(),
    cacheDir: ".mcp-cache",
    watchFiles: true,
    analysisRules: {
      enforceOnPush: true,
      checkAccessibility: true,
      validateImports: true,
      performanceChecks: true,
    },
    completionSettings: {
      includePrivateMembers: false,
      fuzzyMatching: true,
      maxSuggestions: 50,
      prioritizeRecentlyUsed: true,
    },
  };

  constructor() {
    this.config = { ...this.defaultConfig };
  }

  async initialize(): Promise<void> {
    await this.loadConfig();
    this.validateConfig();
  }

  private async loadConfig(): Promise<void> {
    const configPaths = [
      join(process.cwd(), "fincloud-ui-mcp.config.json"),
      join(process.cwd(), ".fincloud-ui-mcp.json"),
      join(process.cwd(), "package.json"),
    ];

    for (const configPath of configPaths) {
      if (existsSync(configPath)) {
        try {
          const configContent = await readFile(configPath, "utf-8");
          const parsedConfig = JSON.parse(configContent);

          if (configPath.endsWith("package.json")) {
            if (parsedConfig.fincloudUiMcp) {
              this.mergeConfig(parsedConfig.fincloudUiMcp);
            }
          } else {
            this.mergeConfig(parsedConfig);
          }

          console.error(`Config loaded from: ${configPath}`);
          break;
        } catch (error) {
          console.error(`Failed to load config from ${configPath}:`, error);
        }
      }
    }
  }

  private mergeConfig(userConfig: Partial<ServerConfig>): void {
    this.config = {
      ...this.config,
      ...userConfig,
      analysisRules: {
        ...this.config.analysisRules,
        ...userConfig.analysisRules,
      },
      completionSettings: {
        ...this.config.completionSettings,
        ...userConfig.completionSettings,
      },
    };
  }

  private validateConfig(): void {
    // Resolve library path to absolute path
    this.config.libraryPath = resolve(this.config.libraryPath);

    if (!existsSync(this.config.libraryPath)) {
      throw new Error(
        `Library path does not exist: ${this.config.libraryPath}\n` +
          `Current working directory: ${process.cwd()}\n` +
          `Please create a 'fincloud-ui-mcp.config.json' file in your project root with the correct 'libraryPath' pointing to your Fincloud UI library.`
      );
    }

    // Validate library structure
    const uiPath = join(this.config.libraryPath, "libs", "ui");
    const utilsPath = join(this.config.libraryPath, "libs", "utils");

    // UI library is required
    if (!existsSync(uiPath)) {
      throw new Error(
        `Required library path missing: ${uiPath}\n` +
          `The library path should point to a directory containing 'libs/ui' and optionally 'libs/utils'.\n` +
          `Current library path: ${this.config.libraryPath}\n` +
          `Please check your configuration and ensure the 'libraryPath' points to the correct Fincloud UI library root directory.`
      );
    }

    // Utils library is optional (for testing purposes)
    if (!existsSync(utilsPath)) {
      console.warn(
        `Utils library path not found: ${utilsPath} (continuing without utils)`
      );
    }

    // Resolve cache directory
    this.config.cacheDir = resolve(
      this.config.libraryPath,
      this.config.cacheDir
    );
  }

  getConfig(): ServerConfig {
    return this.config;
  }

  getLibraryPath(): string {
    return this.config.libraryPath;
  }

  getCacheDir(): string {
    return this.config.cacheDir;
  }

  shouldWatchFiles(): boolean {
    return this.config.watchFiles;
  }

  getAnalysisRules(): AnalysisRules {
    return this.config.analysisRules;
  }

  getCompletionSettings(): CompletionSettings {
    return this.config.completionSettings;
  }

  getUILibPath(): string {
    return join(this.config.libraryPath, "libs", "ui");
  }

  getUtilsLibPath(): string {
    return join(this.config.libraryPath, "libs", "utils");
  }

  hasUtilsLib(): boolean {
    return existsSync(this.getUtilsLibPath());
  }

  getTsConfigPath(): string {
    const basePath = join(this.config.libraryPath, "tsconfig.base.json");
    if (existsSync(basePath)) {
      return basePath;
    }

    // Fallback to regular tsconfig.json
    const regularPath = join(this.config.libraryPath, "tsconfig.json");
    if (existsSync(regularPath)) {
      return regularPath;
    }

    // Use a default if none found
    return basePath;
  }
}
