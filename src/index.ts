#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  McpError,
  ReadResourceRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

import { FincloudUIIndexer } from './indexers/component-indexer.js';
import { CompletionProvider } from './completions/completion-provider.js';
import { DocumentationGenerator } from './documentation/doc-generator.js';
import { CodeAnalyzer } from './analysis/code-analyzer.js';
import { ConfigManager } from './utils/config-manager.js';

class FincloudUIMCPServer {
  private server: Server;
  private indexer: FincloudUIIndexer;
  private completionProvider: CompletionProvider;
  private docGenerator: DocumentationGenerator;
  private codeAnalyzer: CodeAnalyzer;
  private configManager: ConfigManager;

  constructor() {
    this.server = new Server(
      {
        name: 'fincloud-ui-mcp-server',
        version: '1.0.0',
        capabilities: {
          resources: {},
          tools: {},
        },
      }
    );

    this.configManager = new ConfigManager();
    this.indexer = new FincloudUIIndexer(this.configManager);
    this.completionProvider = new CompletionProvider(this.indexer);
    this.docGenerator = new DocumentationGenerator(this.indexer);
    this.codeAnalyzer = new CodeAnalyzer(this.indexer);

    this.setupHandlers();
  }

  private setupHandlers() {
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      return {
        resources: [
          {
            uri: 'fincloud://components',
            mimeType: 'application/json',
            name: 'Fincloud UI Components',
            description: 'List of all available UI components with metadata',
          },
          {
            uri: 'fincloud://utils',
            mimeType: 'application/json',
            name: 'Fincloud Utils',
            description: 'List of all utility functions and services',
          },
          {
            uri: 'fincloud://examples',
            mimeType: 'application/json',
            name: 'Code Examples',
            description: 'Code examples for components and utilities',
          },
        ],
      };
    });

    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const { uri } = request.params;

      switch (uri) {
        case 'fincloud://components':
          return {
            contents: [
              {
                uri,
                mimeType: 'application/json',
                text: JSON.stringify(this.indexer.getComponentRegistry(), null, 2),
              },
            ],
          };

        case 'fincloud://utils':
          return {
            contents: [
              {
                uri,
                mimeType: 'application/json',
                text: JSON.stringify(this.indexer.getUtilsRegistry(), null, 2),
              },
            ],
          };

        case 'fincloud://examples':
          return {
            contents: [
              {
                uri,
                mimeType: 'application/json',
                text: JSON.stringify(this.indexer.getExamples(), null, 2),
              },
            ],
          };

        default:
          throw new McpError(ErrorCode.InvalidRequest, `Unknown resource: ${uri}`);
      }
    });

    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'get_component_completions',
            description: 'Get code completion suggestions for Angular templates and TypeScript',
            inputSchema: {
              type: 'object',
              properties: {
                context: {
                  type: 'string',
                  description: 'The code context where completion is requested',
                },
                position: {
                  type: 'object',
                  properties: {
                    line: { type: 'number' },
                    character: { type: 'number' },
                  },
                  required: ['line', 'character'],
                },
                filePath: {
                  type: 'string',
                  description: 'Path to the file being edited',
                },
              },
              required: ['context', 'position'],
            },
          },
          {
            name: 'get_component_documentation',
            description: 'Generate comprehensive documentation for a component',
            inputSchema: {
              type: 'object',
              properties: {
                componentName: {
                  type: 'string',
                  description: 'Name of the component to document',
                },
                includeExamples: {
                  type: 'boolean',
                  description: 'Whether to include code examples',
                  default: true,
                },
              },
              required: ['componentName'],
            },
          },
          {
            name: 'analyze_code',
            description: 'Analyze code for best practices and potential issues',
            inputSchema: {
              type: 'object',
              properties: {
                filePath: {
                  type: 'string',
                  description: 'Path to the file to analyze',
                },
                code: {
                  type: 'string',
                  description: 'Code content to analyze (optional if filePath provided)',
                },
                analysisType: {
                  type: 'string',
                  enum: ['all', 'performance', 'accessibility', 'best-practices'],
                  description: 'Type of analysis to perform',
                  default: 'all',
                },
              },
            },
          },
          {
            name: 'search_components',
            description: 'Search for components by name, functionality, or usage',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Search query',
                },
                category: {
                  type: 'string',
                  enum: ['form', 'layout', 'navigation', 'data-display', 'feedback', 'all'],
                  description: 'Component category to search within',
                  default: 'all',
                },
                includeUtils: {
                  type: 'boolean',
                  description: 'Whether to include utility functions in search',
                  default: false,
                },
              },
              required: ['query'],
            },
          },
          {
            name: 'get_import_suggestions',
            description: 'Get import path suggestions for components and utilities',
            inputSchema: {
              type: 'object',
              properties: {
                symbol: {
                  type: 'string',
                  description: 'Symbol name to find import for',
                },
                currentFilePath: {
                  type: 'string',
                  description: 'Path of the current file',
                },
              },
              required: ['symbol'],
            },
          },
          {
            name: 'get_related_components',
            description: 'Find components related to a given component',
            inputSchema: {
              type: 'object',
              properties: {
                componentName: {
                  type: 'string',
                  description: 'Name of the component to find related components for',
                },
                relationType: {
                  type: 'string',
                  enum: ['dependencies', 'dependents', 'similar', 'commonly-used-with'],
                  description: 'Type of relationship to find',
                  default: 'commonly-used-with',
                },
              },
              required: ['componentName'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'get_component_completions':
            return await this.handleGetCompletions(args);

          case 'get_component_documentation':
            return await this.handleGetDocumentation(args);

          case 'analyze_code':
            return await this.handleAnalyzeCode(args);

          case 'search_components':
            return await this.handleSearchComponents(args);

          case 'get_import_suggestions':
            return await this.handleGetImportSuggestions(args);

          case 'get_related_components':
            return await this.handleGetRelatedComponents(args);

          default:
            throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new McpError(ErrorCode.InternalError, `Tool execution failed: ${errorMessage}`);
      }
    });
  }

  private async handleGetCompletions(args: any) {
    const { context, position, filePath } = args;
    const completions = await this.completionProvider.getCompletions({
      context,
      position,
      filePath: filePath || 'unknown',
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(completions, null, 2),
        },
      ],
    };
  }

  private async handleGetDocumentation(args: any) {
    const { componentName, includeExamples = true } = args;
    const documentation = await this.docGenerator.generateDocumentation(componentName, {
      includeExamples,
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(documentation, null, 2),
        },
      ],
    };
  }

  private async handleAnalyzeCode(args: any) {
    const { filePath, code, analysisType = 'all' } = args;
    const analysis = await this.codeAnalyzer.analyzeCode({
      filePath,
      code,
      analysisType,
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(analysis, null, 2),
        },
      ],
    };
  }

  private async handleSearchComponents(args: any) {
    const { query, category = 'all', includeUtils = false } = args;
    const results = await this.indexer.searchComponents(query, {
      category,
      includeUtils,
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(results, null, 2),
        },
      ],
    };
  }

  private async handleGetImportSuggestions(args: any) {
    const { symbol, currentFilePath } = args;
    const suggestions = await this.completionProvider.getImportSuggestions(symbol, currentFilePath);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(suggestions, null, 2),
        },
      ],
    };
  }

  private async handleGetRelatedComponents(args: any) {
    const { componentName, relationType = 'commonly-used-with' } = args;
    const related = await this.indexer.getRelatedComponents(componentName, relationType);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(related, null, 2),
        },
      ],
    };
  }

  async run() {
    // Initialize the indexer
    console.error('Initializing Fincloud UI MCP Server...');
    await this.indexer.initialize();
    console.error('Server initialization complete.');

    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Fincloud UI MCP Server running on stdio');
  }
}

const server = new FincloudUIMCPServer();
server.run().catch((error) => {
  console.error('Server error:', error);
  process.exit(1);
});